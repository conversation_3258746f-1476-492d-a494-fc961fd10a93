{"产品线配置": {"引流产品线": {"价格区间": "9.9-19.9元", "目标": "建立信任，获取用户", "产品": {"拼音学习包": {"定价": 9.9, "文件数量": "20-30个", "总页数": "50-80页", "包含内容": ["23个声母卡片（彩色+黑白版）", "24个韵母卡片（彩色+黑白版）", "拼音描红练习册", "拼音拼读游戏卡片", "常见拼音错误纠正指南", "拼音学习进度表"], "关键词匹配": ["拼音", "声母", "韵母", "拼读", "音节"], "封面设计": "温馨卡通风格，突出拼音元素"}, "识字写字包": {"定价": 19.9, "文件数量": "30-50个", "总页数": "80-120页", "包含内容": ["一年级常用汉字表（300字）", "汉字描红练习册", "笔画笔顺标准指南", "汉字组词造句卡片", "田字格练字模板", "识字游戏活动设计"], "关键词匹配": ["识字", "写字", "生字", "汉字", "笔顺", "笔画", "田字格", "描红"], "封面设计": "书法风格，体现汉字美感"}, "数学启蒙包": {"定价": 19.9, "文件数量": "25-40个", "总页数": "60-100页", "包含内容": ["1-100数字认知卡片", "20以内加减法练习册", "数学思维训练题", "图形认知游戏卡片", "数学口诀记忆卡", "家长辅导指南"], "关键词匹配": ["数学", "数字", "加减法", "认识", "比较", "计算"], "封面设计": "数字元素，色彩丰富"}}}, "主力产品线": {"价格区间": "29.9-59.9元", "目标": "核心利润来源", "产品": {"语文同步资料包": {"定价": 39.9, "文件数量": "50-80个", "总页数": "120-200页", "包含内容": ["人教版一年级语文同步练习", "课文重点知识梳理", "看图说话训练材料", "阅读理解专项练习", "作文启蒙指导", "期中期末模拟试卷", "学习进度跟踪表"], "关键词匹配": ["语文", "课文", "阅读", "看图说话", "句子", "词语", "单元"], "封面设计": "学院风格，体现专业性"}, "数学全科包": {"定价": 39.9, "文件数量": "40-70个", "总页数": "100-150页", "包含内容": ["数与运算专项训练", "图形与几何基础练习", "数学思维拓展题", "应用题解题技巧", "数学游戏活动设计", "期中期末复习宝典", "错题分析与纠正"], "关键词匹配": ["数学", "专项", "练习", "训练", "应用题", "思维"], "封面设计": "科技感，突出思维训练"}, "期末冲刺包": {"定价": 59.9, "文件数量": "60-100个", "总页数": "150-250页", "包含内容": ["语数英三科重点知识汇总", "期末模拟试卷套装（30套）", "易错题专项突破", "考试技巧与心理调适", "复习计划时间表", "家长陪考指南", "考后分析模板"], "关键词匹配": ["期中", "期末", "测试", "考试", "复习", "冲刺"], "封面设计": "冲刺感，红色主调"}}}, "高端产品线": {"价格区间": "99.9-299.9元", "目标": "提升客单价", "产品": {"全年学习资料大全": {"定价": 199.9, "文件数量": "200+个", "总页数": "500+页", "包含内容": ["包含以上所有单品内容", "独家学习方法指导手册", "家长辅导技巧大全", "一年级全年学习规划", "专属学习群终身答疑服务", "每月新增资料更新", "一对一学习问题咨询（3次）"], "关键词匹配": ["全部分类"], "封面设计": "豪华版设计，金色元素"}}}}, "包装标准": {"文件命名规范": {"格式": "【产品名】-【序号】-【具体内容】.pdf", "示例": "【拼音学习包】-01-声母认知卡片.pdf"}, "目录结构": {"一级目录": "产品名称", "二级目录": ["基础练习", "专项训练", "测试卷", "指导手册"], "三级目录": "具体分类"}, "质量要求": {"分辨率": "300DPI以上", "文件格式": "PDF优先，DOCX辅助", "页面设置": "A4纸张，适合打印", "水印要求": "添加品牌水印，不影响使用"}, "封面设计": {"尺寸": "210×297mm（A4）", "元素": ["产品名称", "适用年级", "内容概述", "品牌标识"], "风格": "温馨教育风，符合家长审美"}}, "自动化流程": {"第一步": "运行资料整理工具.py进行智能分类", "第二步": "根据分类结果生成产品包清单", "第三步": "使用复制脚本批量整理文件", "第四步": "添加封面和目录", "第五步": "质量检查和最终打包"}, "时间安排": {"第1天": "运行自动化工具，完成分类和清单生成", "第2天": "批量复制文件，创建目录结构", "第3天": "制作封面和使用说明", "第4天": "质量检查和优化调整", "第5天": "最终打包和上架准备"}}