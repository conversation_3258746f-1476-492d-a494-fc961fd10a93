#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一年级学习资料智能整理工具
功能：自动分析、分类、去重、打包资料
"""

import pandas as pd
import os
import shutil
import re
from pathlib import Path
from collections import defaultdict
import json
from datetime import datetime

class MaterialOrganizer:
    def __init__(self, csv_file_path):
        """初始化资料整理器"""
        self.csv_file = csv_file_path
        self.df = pd.read_csv(csv_file_path, encoding='utf-8')
        self.categories = self._init_categories()
        self.products = self._init_products()
        
    def _init_categories(self):
        """初始化分类规则"""
        return {
            '拼音学习': {
                'keywords': ['拼音', '声母', '韵母', '拼读', '音节'],
                'priority': 1
            },
            '识字写字': {
                'keywords': ['识字', '写字', '生字', '汉字', '笔顺', '笔画', '田字格', '描红'],
                'priority': 2
            },
            '数学基础': {
                'keywords': ['数学', '数字', '加减法', '认识', '比较', '计算', '应用题'],
                'priority': 3
            },
            '语文综合': {
                'keywords': ['语文', '课文', '阅读', '看图说话', '句子', '词语'],
                'priority': 4
            },
            '期中期末': {
                'keywords': ['期中', '期末', '测试', '考试', '复习'],
                'priority': 5
            },
            '单元测试': {
                'keywords': ['单元', '第一单元', '第二单元', '第三单元', '第四单元', '第五单元', '第六单元', '第七单元', '第八单元'],
                'priority': 6
            },
            '专项练习': {
                'keywords': ['专项', '练习', '训练', '强化'],
                'priority': 7
            },
            '英语启蒙': {
                'keywords': ['英语', 'English', '单词', '字母'],
                'priority': 8
            }
        }
    
    def _init_products(self):
        """初始化产品包装方案"""
        return {
            '拼音学习包': {
                'price': 9.9,
                'categories': ['拼音学习'],
                'description': '一年级拼音全攻略',
                'target_size': '50-100页'
            },
            '识字写字包': {
                'price': 19.9,
                'categories': ['识字写字'],
                'description': '一年级识字写字宝典',
                'target_size': '80-120页'
            },
            '数学启蒙包': {
                'price': 19.9,
                'categories': ['数学基础'],
                'description': '一年级数学基础训练营',
                'target_size': '60-100页'
            },
            '语文同步包': {
                'price': 39.9,
                'categories': ['语文综合', '单元测试'],
                'description': '一年级语文全能训练',
                'target_size': '120-200页'
            },
            '数学全科包': {
                'price': 39.9,
                'categories': ['数学基础', '专项练习'],
                'description': '一年级数学思维训练营',
                'target_size': '100-150页'
            },
            '期末冲刺包': {
                'price': 59.9,
                'categories': ['期中期末', '专项练习'],
                'description': '一年级期末冲刺宝典',
                'target_size': '150-250页'
            },
            '全年学习大全': {
                'price': 199.9,
                'categories': ['拼音学习', '识字写字', '数学基础', '语文综合', '期中期末', '单元测试'],
                'description': '一年级全年无忧学习宝典',
                'target_size': '500+页'
            }
        }

    def analyze_files(self):
        """分析文件分布"""
        print("📊 开始分析文件分布...")
        
        # 按文件类型统计
        file_types = self.df['文件类型'].value_counts()
        print(f"\n📁 文件类型分布:")
        for file_type, count in file_types.items():
            print(f"  {file_type}: {count}个文件")
        
        # 按大小统计
        total_size = self.df['文件大小'].sum()
        print(f"\n💾 总文件大小: {total_size:.2f}MB")
        
        # 重复文件统计
        duplicates = self.df[self.df['是否重复'] == 'Y']
        print(f"\n🔄 重复文件: {len(duplicates)}个")
        
        return {
            'file_types': file_types.to_dict(),
            'total_size': total_size,
            'duplicates_count': len(duplicates)
        }

    def categorize_files(self):
        """智能分类文件"""
        print("\n🎯 开始智能分类...")
        
        categorized = defaultdict(list)
        uncategorized = []
        
        for idx, row in self.df.iterrows():
            filename = row['原资料名'].lower()
            best_category = None
            best_score = 0
            
            # 计算每个分类的匹配分数
            for category, config in self.categories.items():
                score = 0
                for keyword in config['keywords']:
                    if keyword in filename:
                        score += 1
                
                # 优先级加权
                score = score * config['priority']
                
                if score > best_score:
                    best_score = score
                    best_category = category
            
            if best_category:
                categorized[best_category].append(row)
            else:
                uncategorized.append(row)
        
        # 输出分类结果
        print(f"\n📋 分类结果:")
        for category, files in categorized.items():
            total_size = sum([f['文件大小'] for f in files])
            print(f"  {category}: {len(files)}个文件, {total_size:.2f}MB")
        
        print(f"  未分类: {len(uncategorized)}个文件")
        
        return categorized, uncategorized

    def create_product_packages(self, categorized_files):
        """创建产品包"""
        print("\n📦 开始创建产品包...")
        
        packages = {}
        
        for product_name, config in self.products.items():
            package_files = []
            total_size = 0
            
            # 收集相关分类的文件
            for category in config['categories']:
                if category in categorized_files:
                    for file_info in categorized_files[category]:
                        # 跳过重复文件
                        if file_info['是否重复'] == 'N':
                            package_files.append(file_info)
                            total_size += file_info['文件大小']
            
            packages[product_name] = {
                'files': package_files,
                'total_size': total_size,
                'file_count': len(package_files),
                'price': config['price'],
                'description': config['description']
            }
            
            print(f"  {product_name}: {len(package_files)}个文件, {total_size:.2f}MB, 定价{config['price']}元")
        
        return packages

    def generate_file_lists(self, packages, output_dir='产品包清单'):
        """生成产品包文件清单"""
        print(f"\n📝 生成产品包清单到 {output_dir}...")
        
        os.makedirs(output_dir, exist_ok=True)
        
        for product_name, package_info in packages.items():
            # 创建Excel清单
            df_package = pd.DataFrame(package_info['files'])
            excel_path = os.path.join(output_dir, f"{product_name}_文件清单.xlsx")
            df_package.to_excel(excel_path, index=False, encoding='utf-8')
            
            # 创建文本清单
            txt_path = os.path.join(output_dir, f"{product_name}_文件清单.txt")
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(f"# {product_name}\n")
                f.write(f"定价: {package_info['price']}元\n")
                f.write(f"描述: {package_info['description']}\n")
                f.write(f"文件数量: {package_info['file_count']}个\n")
                f.write(f"总大小: {package_info['total_size']:.2f}MB\n\n")
                f.write("## 文件列表:\n")
                
                for i, file_info in enumerate(package_info['files'], 1):
                    f.write(f"{i}. {file_info['原资料名']} ({file_info['文件大小']:.2f}MB)\n")
        
        print(f"✅ 清单已生成到 {output_dir} 目录")

    def create_copy_scripts(self, packages, output_dir='复制脚本'):
        """生成文件复制脚本"""
        print(f"\n🔧 生成文件复制脚本到 {output_dir}...")
        
        os.makedirs(output_dir, exist_ok=True)
        
        for product_name, package_info in packages.items():
            script_path = os.path.join(output_dir, f"复制_{product_name}.bat")
            
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(f"@echo off\n")
                f.write(f"echo 正在复制 {product_name} 相关文件...\n")
                f.write(f"mkdir \"{product_name}\"\n\n")
                
                for file_info in package_info['files']:
                    src_path = file_info['完整路径']
                    dst_path = f"{product_name}\\{file_info['原资料名']}"
                    f.write(f"copy \"{src_path}\" \"{dst_path}\"\n")
                
                f.write(f"\necho {product_name} 文件复制完成!\n")
                f.write("pause\n")
        
        print(f"✅ 复制脚本已生成到 {output_dir} 目录")

    def generate_report(self, packages):
        """生成整理报告"""
        print("\n📊 生成整理报告...")
        
        report = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_files': len(self.df),
            'total_size': self.df['文件大小'].sum(),
            'products': {}
        }
        
        for product_name, package_info in packages.items():
            report['products'][product_name] = {
                'file_count': package_info['file_count'],
                'total_size': package_info['total_size'],
                'price': package_info['price'],
                'description': package_info['description']
            }
        
        # 保存报告
        with open('整理报告.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print(f"\n📋 整理摘要:")
        print(f"  总文件数: {report['total_files']}")
        print(f"  总大小: {report['total_size']:.2f}MB")
        print(f"  产品包数: {len(packages)}")
        
        total_value = sum([p['price'] for p in report['products'].values()])
        print(f"  总价值: {total_value}元")
        
        return report

def main():
    """主函数"""
    print("🚀 一年级学习资料智能整理工具启动...")
    
    # 初始化整理器
    csv_file = "文件统计报告_20250821_114332.csv"
    organizer = MaterialOrganizer(csv_file)
    
    # 分析文件
    analysis = organizer.analyze_files()
    
    # 智能分类
    categorized, uncategorized = organizer.categorize_files()
    
    # 创建产品包
    packages = organizer.create_product_packages(categorized)
    
    # 生成清单和脚本
    organizer.generate_file_lists(packages)
    organizer.create_copy_scripts(packages)
    
    # 生成报告
    report = organizer.generate_report(packages)
    
    print("\n🎉 整理完成! 请查看生成的文件夹和报告。")

if __name__ == "__main__":
    main()
