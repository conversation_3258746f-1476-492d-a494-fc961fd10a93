#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品封面和说明书生成器
功能：自动生成产品封面、目录、使用说明
"""

from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.colors import Color, black, white, blue, red
from reportlab.lib.units import cm
from reportlab.pdfbase import pdfutils
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
import json
import os
from datetime import datetime

class CoverGenerator:
    def __init__(self, config_file="产品包装配置.json"):
        """初始化封面生成器"""
        with open(config_file, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        # 注册中文字体（需要下载字体文件）
        try:
            pdfmetrics.registerFont(TTFont('SimHei', 'SimHei.ttf'))
            pdfmetrics.registerFont(TTFont('SimSun', 'SimSun.ttf'))
            self.font_available = True
        except:
            print("⚠️ 中文字体文件未找到，将使用默认字体")
            self.font_available = False
    
    def create_cover(self, product_name, product_info, output_dir="产品封面"):
        """创建产品封面"""
        os.makedirs(output_dir, exist_ok=True)
        
        filename = os.path.join(output_dir, f"{product_name}_封面.pdf")
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        # 设置字体
        if self.font_available:
            title_font = 'SimHei'
            content_font = 'SimSun'
        else:
            title_font = 'Helvetica-Bold'
            content_font = 'Helvetica'
        
        # 背景色
        c.setFillColor(Color(0.95, 0.98, 1.0))  # 淡蓝色背景
        c.rect(0, 0, width, height, fill=1)
        
        # 标题区域
        c.setFillColor(Color(0.2, 0.4, 0.8))  # 深蓝色
        c.rect(0, height-8*cm, width, 8*cm, fill=1)
        
        # 产品名称
        c.setFillColor(white)
        c.setFont(title_font, 36)
        text_width = c.stringWidth(product_name, title_font, 36)
        c.drawString((width - text_width) / 2, height - 4*cm, product_name)
        
        # 副标题
        subtitle = product_info.get('包含内容', ['专业教辅资料'])[0]
        c.setFont(content_font, 18)
        text_width = c.stringWidth(subtitle, content_font, 18)
        c.drawString((width - text_width) / 2, height - 5.5*cm, subtitle)
        
        # 价格标签
        price = f"￥{product_info['定价']}"
        c.setFillColor(Color(1, 0.3, 0.3))  # 红色
        c.rect(width - 6*cm, height - 12*cm, 5*cm, 2*cm, fill=1)
        c.setFillColor(white)
        c.setFont(title_font, 24)
        text_width = c.stringWidth(price, title_font, 24)
        c.drawString(width - 6*cm + (5*cm - text_width) / 2, height - 11.2*cm, price)
        
        # 内容概述
        c.setFillColor(black)
        c.setFont(content_font, 14)
        y_position = height - 15*cm
        
        contents = product_info.get('包含内容', [])
        for i, content in enumerate(contents[:6]):  # 最多显示6项
            c.drawString(3*cm, y_position - i*0.8*cm, f"✓ {content}")
        
        # 底部信息
        c.setFont(content_font, 12)
        c.drawString(2*cm, 3*cm, f"适用年级：一年级")
        c.drawString(2*cm, 2.5*cm, f"文件数量：{product_info.get('文件数量', '若干')}个")
        c.drawString(2*cm, 2*cm, f"总页数：{product_info.get('总页数', '丰富')}页")
        
        # 品牌标识
        c.setFont(content_font, 10)
        c.drawString(width - 8*cm, 1*cm, f"制作时间：{datetime.now().strftime('%Y年%m月')}")
        
        c.save()
        print(f"✅ {product_name} 封面已生成: {filename}")
        
    def create_manual(self, product_name, product_info, file_list, output_dir="使用说明"):
        """创建使用说明书"""
        os.makedirs(output_dir, exist_ok=True)
        
        filename = os.path.join(output_dir, f"{product_name}_使用说明.pdf")
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        # 设置字体
        if self.font_available:
            title_font = 'SimHei'
            content_font = 'SimSun'
        else:
            title_font = 'Helvetica-Bold'
            content_font = 'Helvetica'
        
        # 第一页：使用说明
        c.setFont(title_font, 24)
        c.drawString(3*cm, height - 3*cm, f"{product_name} 使用说明")
        
        # 使用方法
        c.setFont(title_font, 16)
        c.drawString(2*cm, height - 5*cm, "📖 使用方法")
        
        instructions = [
            "1. 下载所有文件到电脑本地",
            "2. 建议使用PDF阅读器打开文件",
            "3. 可以直接打印使用，推荐A4纸张",
            "4. 建议每天练习15-20分钟",
            "5. 家长可参考指导手册进行辅导"
        ]
        
        c.setFont(content_font, 12)
        y_pos = height - 6*cm
        for instruction in instructions:
            c.drawString(2.5*cm, y_pos, instruction)
            y_pos -= 0.8*cm
        
        # 学习建议
        c.setFont(title_font, 16)
        c.drawString(2*cm, y_pos - 1*cm, "💡 学习建议")
        
        suggestions = [
            "• 循序渐进，不要急于求成",
            "• 多鼓励孩子，建立学习信心",
            "• 结合游戏，让学习更有趣",
            "• 定期复习，巩固学习效果",
            "• 有问题及时联系客服答疑"
        ]
        
        c.setFont(content_font, 12)
        y_pos -= 2*cm
        for suggestion in suggestions:
            c.drawString(2.5*cm, y_pos, suggestion)
            y_pos -= 0.8*cm
        
        # 联系方式
        c.setFont(title_font, 16)
        c.drawString(2*cm, y_pos - 1*cm, "📞 售后服务")
        
        c.setFont(content_font, 12)
        y_pos -= 2*cm
        c.drawString(2.5*cm, y_pos, "如有任何问题，请联系客服：")
        c.drawString(2.5*cm, y_pos - 0.8*cm, "• 微信：[您的微信号]")
        c.drawString(2.5*cm, y_pos - 1.6*cm, "• 服务时间：9:00-21:00")
        c.drawString(2.5*cm, y_pos - 2.4*cm, "• 承诺：24小时内回复")
        
        c.showPage()
        
        # 第二页：文件目录
        c.setFont(title_font, 24)
        c.drawString(3*cm, height - 3*cm, "📁 文件目录")
        
        c.setFont(content_font, 10)
        y_pos = height - 5*cm
        
        for i, file_info in enumerate(file_list[:50], 1):  # 最多显示50个文件
            filename = file_info.get('原资料名', f'文件{i}')
            size = file_info.get('文件大小', 0)
            
            # 文件名过长时截断
            if len(filename) > 40:
                filename = filename[:37] + "..."
            
            c.drawString(1*cm, y_pos, f"{i:2d}.")
            c.drawString(2*cm, y_pos, filename)
            c.drawString(width - 3*cm, y_pos, f"{size:.1f}MB")
            
            y_pos -= 0.5*cm
            
            # 换页
            if y_pos < 2*cm and i < len(file_list):
                c.showPage()
                c.setFont(title_font, 18)
                c.drawString(3*cm, height - 3*cm, "📁 文件目录（续）")
                c.setFont(content_font, 10)
                y_pos = height - 5*cm
        
        c.save()
        print(f"✅ {product_name} 使用说明已生成: {filename}")

    def create_all_covers(self):
        """批量生成所有产品的封面和说明"""
        print("🎨 开始批量生成封面和说明书...")
        
        for category_name, category_info in self.config['产品线配置'].items():
            print(f"\n📦 处理 {category_name}...")
            
            for product_name, product_info in category_info['产品'].items():
                # 生成封面
                self.create_cover(product_name, product_info)
                
                # 生成说明书（需要文件列表，这里用示例）
                sample_files = [
                    {'原资料名': f'{product_name}示例文件1.pdf', '文件大小': 2.5},
                    {'原资料名': f'{product_name}示例文件2.pdf', '文件大小': 1.8},
                ]
                self.create_manual(product_name, product_info, sample_files)

def main():
    """主函数"""
    print("🎨 产品封面和说明书生成器启动...")
    
    generator = CoverGenerator()
    generator.create_all_covers()
    
    print("\n🎉 所有封面和说明书生成完成!")
    print("📁 请查看 '产品封面' 和 '使用说明' 文件夹")

if __name__ == "__main__":
    main()
