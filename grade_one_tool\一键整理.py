#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键整理打包工具
功能：自动完成从分析到打包的全流程
"""

import os
import sys
import subprocess
import time
from datetime import datetime

class OneClickOrganizer:
    def __init__(self):
        """初始化一键整理器"""
        self.start_time = datetime.now()
        self.steps = [
            "📊 文件分析与分类",
            "📦 产品包创建", 
            "📋 清单生成",
            "🔧 复制脚本生成",
            "🎨 封面制作",
            "📖 说明书生成",
            "✅ 质量检查",
            "📦 最终打包"
        ]
        self.current_step = 0
        
    def print_header(self):
        """打印工具头部信息"""
        print("=" * 60)
        print("🚀 一年级学习资料一键整理打包工具")
        print("=" * 60)
        print(f"⏰ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 工作目录: {os.getcwd()}")
        print("=" * 60)
        
    def print_step(self, step_name):
        """打印当前步骤"""
        self.current_step += 1
        print(f"\n[{self.current_step}/{len(self.steps)}] {step_name}")
        print("-" * 40)
        
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查运行环境...")
        
        required_files = [
            "文件统计报告_20250821_114332.csv",
            "资料整理工具.py",
            "产品包装配置.json",
            "封面生成器.py"
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print("❌ 缺少必要文件:")
            for file in missing_files:
                print(f"   - {file}")
            return False
        
        print("✅ 环境检查通过")
        return True
        
    def run_analysis(self):
        """运行文件分析"""
        self.print_step("📊 文件分析与分类")
        
        try:
            result = subprocess.run([sys.executable, "资料整理工具.py"], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print("✅ 文件分析完成")
                print(result.stdout)
            else:
                print("❌ 文件分析失败")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 运行分析工具时出错: {e}")
            return False
            
        return True
        
    def generate_covers(self):
        """生成封面和说明书"""
        self.print_step("🎨 封面和说明书生成")
        
        try:
            # 检查是否需要安装reportlab
            try:
                import reportlab
            except ImportError:
                print("📦 安装reportlab库...")
                subprocess.run([sys.executable, "-m", "pip", "install", "reportlab"], 
                             check=True)
            
            result = subprocess.run([sys.executable, "封面生成器.py"], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print("✅ 封面和说明书生成完成")
            else:
                print("⚠️ 封面生成可能有问题，但继续执行")
                print(result.stderr)
                
        except Exception as e:
            print(f"⚠️ 生成封面时出错: {e}")
            print("继续执行其他步骤...")
            
        return True
        
    def create_final_packages(self):
        """创建最终产品包"""
        self.print_step("📦 最终产品包创建")
        
        # 创建产品包目录结构
        packages = [
            "拼音学习包",
            "识字写字包", 
            "数学启蒙包",
            "语文同步资料包",
            "数学全科包",
            "期末冲刺包",
            "全年学习资料大全"
        ]
        
        base_dir = "最终产品包"
        os.makedirs(base_dir, exist_ok=True)
        
        for package in packages:
            package_dir = os.path.join(base_dir, package)
            os.makedirs(package_dir, exist_ok=True)
            
            # 创建子目录
            subdirs = ["01-基础练习", "02-专项训练", "03-测试卷", "04-指导手册"]
            for subdir in subdirs:
                os.makedirs(os.path.join(package_dir, subdir), exist_ok=True)
            
            print(f"✅ 创建目录: {package}")
        
        return True
        
    def create_readme(self):
        """创建README文件"""
        self.print_step("📖 创建使用说明")
        
        readme_content = f"""# 一年级学习资料整理结果

## 📊 整理统计
- 整理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 工具版本: v1.0
- 处理文件: 基于文件统计报告

## 📁 目录结构

### 产品包清单/
包含各产品的文件清单Excel表格

### 复制脚本/
包含批量复制文件的bat脚本

### 产品封面/
包含各产品的封面PDF文件

### 使用说明/
包含各产品的使用说明书

### 最终产品包/
按产品分类的目录结构，用于最终文件整理

## 🚀 使用方法

1. 查看"产品包清单"了解每个产品包含的文件
2. 运行"复制脚本"中的bat文件批量复制文件
3. 将复制的文件按目录结构整理到"最终产品包"
4. 添加封面和说明书到对应产品包
5. 压缩打包即可销售

## 📞 技术支持

如有问题请联系开发者。

---
自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        with open("README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
            
        print("✅ README.md 已创建")
        return True
        
    def quality_check(self):
        """质量检查"""
        self.print_step("✅ 质量检查")
        
        check_items = [
            ("产品包清单", "产品包清单"),
            ("复制脚本", "复制脚本"), 
            ("产品封面", "产品封面"),
            ("使用说明", "使用说明"),
            ("最终产品包", "最终产品包"),
            ("整理报告", "整理报告.json")
        ]
        
        all_good = True
        for item_name, path in check_items:
            if os.path.exists(path):
                print(f"✅ {item_name}: 存在")
            else:
                print(f"❌ {item_name}: 缺失")
                all_good = False
        
        return all_good
        
    def print_summary(self):
        """打印总结"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print("\n" + "=" * 60)
        print("🎉 整理完成!")
        print("=" * 60)
        print(f"⏰ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  总耗时: {duration.total_seconds():.1f}秒")
        
        print("\n📁 生成的文件夹:")
        folders = ["产品包清单", "复制脚本", "产品封面", "使用说明", "最终产品包"]
        for folder in folders:
            if os.path.exists(folder):
                file_count = len(os.listdir(folder))
                print(f"   📂 {folder}: {file_count}个文件")
        
        print("\n🚀 下一步操作:")
        print("   1. 查看产品包清单，了解文件分布")
        print("   2. 运行复制脚本，批量整理文件")
        print("   3. 添加封面和说明书到产品包")
        print("   4. 压缩打包，准备销售")
        
        print("\n💡 提示:")
        print("   - 所有操作都是自动化的，大大提高效率")
        print("   - 可以根据需要调整产品包内容")
        print("   - 建议先小批量测试，再大规模应用")
        
    def run(self):
        """运行完整流程"""
        self.print_header()
        
        # 检查环境
        if not self.check_dependencies():
            print("❌ 环境检查失败，请检查必要文件")
            return False
        
        # 执行各个步骤
        steps = [
            self.run_analysis,
            self.generate_covers,
            self.create_final_packages,
            self.create_readme,
            self.quality_check
        ]
        
        for step_func in steps:
            if not step_func():
                print("❌ 步骤执行失败，停止运行")
                return False
            time.sleep(1)  # 短暂暂停
        
        self.print_summary()
        return True

def main():
    """主函数"""
    organizer = OneClickOrganizer()
    success = organizer.run()
    
    if success:
        print("\n🎊 恭喜！一键整理完成！")
    else:
        print("\n😞 整理过程中遇到问题，请检查错误信息")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
